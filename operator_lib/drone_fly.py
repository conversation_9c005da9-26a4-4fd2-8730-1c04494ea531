import cv2
import numpy as np
import torch
from operator_lib.depth_anything_v2.dpt import DepthAnythingV2
import os

import multiprocessing as mp
_cached_depth_model = None
_model_process_id = None  # 记录模型初始化时的进程ID

# 使用相对路径
ckpt_path = os.path.join("static", "depth_anything_v2_metric_vkitti_vitl.pth")


def init_depth_model(
        input_size=518,
        max_depth=65.0,
        encoder='vitl',
        ckpt_path=ckpt_path
):
    """初始化深度估计模型"""
    global _cached_depth_model, _model_process_id
    current_process_id = os.getpid()
    
    # 如果模型未初始化或在不同进程中，需要重新初始化
    if _cached_depth_model is None or _model_process_id != current_process_id:
        try:
            # 检查模型文件是否存在
            if not os.path.exists(ckpt_path):
                raise FileNotFoundError(f"深度估计模型文件不存在: {ckpt_path}")
            
            print(f"正在加载深度估计模型: {ckpt_path}")
            
            device = 'cuda' if torch.cuda.is_available() else 'cpu'
            print(f"使用设备: {device}")
            
            model_configs = {
                'vitl': {'encoder': 'vitl', 'features': 256, 'out_channels': [256, 512, 1024, 1024]},
            }
            
            # 初始化模型结构
            model = DepthAnythingV2(**{**model_configs[encoder], 'max_depth': max_depth})
            print("模型结构初始化完成")
            
            # 加载模型权重
            try:
                state_dict = torch.load(ckpt_path, map_location=device)
                model.load_state_dict(state_dict)
                print("模型权重加载完成")
            except Exception as e:
                raise Exception(f"加载模型权重失败: {str(e)}")
            
            # 将模型移到设备并设为评估模式
            model = model.to(device).eval()
            model.device = device
            _cached_depth_model = model
            _model_process_id = current_process_id  # 记录初始化时的进程ID
            print(f"深度估计模型在进程 {current_process_id} 中初始化成功")
            
        except Exception as e:
            print(f"深度估计模型初始化失败: {str(e)}")
            raise e
    
    return _cached_depth_model


def estimate_depth(image: np.ndarray, scale_factor=1.9396) -> float:
    """估计图像中心点的深度值"""
    try:
        # 检查输入图像的有效性
        if image is None:
            raise ValueError("输入图像为None")
        
        if not isinstance(image, np.ndarray):
            raise ValueError(f"输入不是numpy数组，类型: {type(image)}")
        
        if image.size == 0:
            raise ValueError("输入图像为空")
        
        if len(image.shape) != 3:
            raise ValueError(f"图像维度错误，期望3维，实际{len(image.shape)}维")
        
        h, w, c = image.shape
        if h <= 0 or w <= 0 or c != 3:
            raise ValueError(f"图像尺寸无效: {h}x{w}x{c}")
        
        print(f"深度估计输入验证通过: {h}x{w}x{c}, dtype: {image.dtype}")
        
        model = init_depth_model()
        depth_map = model.infer_image(image, input_size=518)
        h, w = depth_map.shape
        return float(depth_map[h // 2, w // 2] * scale_factor)
    except Exception as e:
        print(f"深度估计过程出错: {str(e)}")
        raise e


def init_drone_fly_context():
    """初始化绕飞巡检上下文"""
    return {
        "fly_count": 0,  # 绕飞面
        "gimbal_align_count": 0,
        "depth_control": False,
        "last_depth_control": False,
        "gimbal_control": False,
        "init_drone": 0,
        "init_gimbal": 0,
        "depth_buffer": [],
        "frame_count": 0,
        "detect_state": 0,
        "depth_threshold": 1.0,
    }
