import cv2
import multiprocessing as mp
import numpy as np
import os
import subprocess
import time

from config.config import logger
from operator_lib.drone_fly import init_drone_fly_context, \
    estimate_depth
from operator_lib.gimbal_align import process_gimbal_alignment, reset_gimbal_align_state, CONFIDENCE_THRESHOLDS, \
    CLASS_NAME_MAPPING, GimbalAlignResult
from service import device_control
from utils import draw_chinese_text, make_msg_resp, draw_fps_time, validate_frame_integrity

# 多进程共享变量
_exit_event = None

gimbal_control_enabled = None  # 云台控制逻辑开关
drone_fly_doing = None  # 绕飞巡检是否正在执行。为了避免重复执行来自深度决策的行为
drone_fly_context = None  # 绕飞巡检状态
current_executing_action_type = None  # 当前正在执行的行为类型

# 设备行为序列相关共享变量
device_action_queue = None  # 设备行为序列队列
gimbal_sub_action_queue = None  # 云台对准拍照子行为序列队列

drone_control_p = 4  # p为无人机两三个身位
drone_control_n = 6.5  # 右移 n 米，推桥底中间


# ===== 全局变量更新函数 =====
def create_exit_event():
    """创建新的drone_control_exit_event"""
    global _exit_event
    _exit_event = mp.Event()


def get_exit_event():
    """获取当前的drone_control_exit_event"""
    return _exit_event


def update_gimbal_control(state: bool):
    global gimbal_control_enabled
    if gimbal_control_enabled is not None:
        gimbal_control_enabled.value = state
        logger.info(f"云台控制状态已更新：{'开启' if state else '关闭'}")
    else:
        logger.warning("gimbal_control_enabled 共享变量未初始化")


def update_drone_fly_doing(state: bool):
    """更新绕飞巡检动作执行锁状态"""
    global drone_fly_doing
    if drone_fly_doing is not None:
        drone_fly_doing.value = state
        logger.info(f"绕飞巡检动作执行锁已更新：{'开启' if state else '关闭'}")
    else:
        logger.warning("drone_fly_doing 共享变量未初始化")


def update_drone_fly_context(key: str, value):
    """更新绕飞巡检上下文中的特定字段"""
    global drone_fly_context
    if drone_fly_context is not None:
        drone_fly_context[key] = value
        logger.info(f"绕飞巡检上下文已更新：{key} = {value}")
    else:
        logger.warning("drone_fly_context 共享变量未初始化")


def update_current_executing_action_type(action_type):
    """更新当前正在执行的行为类型"""
    global current_executing_action_type
    if current_executing_action_type is not None:
        current_executing_action_type.value = action_type if action_type else ""
        logger.info(f"当前执行行为类型已更新：{action_type}")
    else:
        logger.warning("current_executing_action_type 共享变量未初始化")


def get_current_executing_action_type():
    """获取当前正在执行的行为类型"""
    global current_executing_action_type
    if current_executing_action_type is not None:
        return current_executing_action_type.value
    return None


def add_device_action(action_data):
    """添加设备行为到行为序列"""
    global device_action_queue
    if device_action_queue is not None:
        device_action_queue.put(action_data)
        logger.info(f"设备行为已添加到序列：{action_data}")
    else:
        logger.warning("device_action_queue 共享变量未初始化")


def clear_device_action():
    """清空设备行为序列"""
    global device_action_queue
    if device_action_queue is not None:
        # multiprocessing.Queue没有clear方法，需要通过循环清空
        try:
            while not device_action_queue.empty():
                device_action_queue.get_nowait()
        except mp.queues.Empty:
            pass
        logger.info(f"设备行为序列已清空")
    else:
        logger.warning("device_action_queue 共享变量未初始化")


def add_gimbal_sub_action(action_data):
    """添加云台子行为到子行为序列"""
    global gimbal_sub_action_queue
    if gimbal_sub_action_queue is not None:
        gimbal_sub_action_queue.put(action_data)
        logger.info(f"云台子行为已添加到序列：{action_data}")
    else:
        logger.warning("gimbal_sub_action_queue 共享变量未初始化")


def clear_gimbal_sub_action(action_data):
    """清空云台对准子行为序列"""
    global gimbal_sub_action_queue
    if gimbal_sub_action_queue is not None:
        gimbal_sub_action_queue.put(action_data)
        logger.info(f"清空云台对准子行为序列")
    else:
        logger.warning("gimbal_sub_action_queue 共享变量未初始化")


def init_worker(exit_evt, shared_vars=None):
    """初始化控制工作进程
    Args:
        exit_evt: 退出事件
        shared_vars: 共享变量字典
    """
    global _exit_event, gimbal_control_enabled, drone_fly_doing, drone_fly_context
    global device_action_queue, gimbal_sub_action_queue, current_executing_action_type

    _exit_event = exit_evt

    if shared_vars:
        gimbal_control_enabled = shared_vars['gimbal_control_enabled']
        drone_fly_doing = shared_vars.get('drone_fly_doing')
        drone_fly_context = shared_vars.get('drone_fly_context')
        device_action_queue = shared_vars.get('device_action_queue')
        gimbal_sub_action_queue = shared_vars.get('gimbal_sub_action_queue')
        current_executing_action_type = shared_vars.get('current_executing_action_type')


def run_capture_video(exit_evt, shared_vars, *args):
    """包装函数，先初始化再运行capture_video"""
    init_worker(exit_evt, shared_vars)
    capture_video(*args)


def run_process_frames(exit_evt, shared_vars, *args):
    """包装函数，先初始化再运行process_frames"""
    init_worker(exit_evt, shared_vars)
    process_frames(*args)


def run_push_to_ffmpeg(exit_evt, shared_vars, *args):
    """包装函数，先初始化再运行push_to_ffmpeg"""
    init_worker(exit_evt, shared_vars)
    push_to_ffmpeg(*args)


def run_device_control(exit_evt, shared_vars, *args):
    """包装函数，先初始化再运行device_control_process"""
    init_worker(exit_evt, shared_vars)
    device_control_process(*args)


def handle_error(err, err_queue):
    """处理控制错误"""
    # 将错误放入队列
    err_queue.put(err)
    # 设置退出事件
    _exit_event.set()


def capture_video(pull_stream_url, frame_queue, err_queue):
    """控制模式的视频捕获进程"""
    try:
        cap = cv2.VideoCapture(pull_stream_url)
        if cap.isOpened():
            # 设置缓冲区大小为1，减少延迟
            cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            # 设置超时，避免卡死
            cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 5000)
            cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)
        else:
            cap = None
    except Exception as e:
        logger.error(f"创建视频捕获对象时发生异常: {str(e)}")
        cap = None
    if cap is None:
        err = f"failed to open source video: {pull_stream_url}"
        logger.error(err)
        handle_error(err, err_queue)
        return

    # 配置参数
    config = {
        'max_failures': 30,  # 最大连续失败次数阈值
        'max_corrupted_frames': 10,  # 最大连续损坏帧数阈值
        'retry_interval': 0.1  # 重试间隔时间
    }

    # 计数器
    counters = {
        'consecutive_failures': 0,  # 连续失败次数
        'corrupted_frame_count': 0  # 连续损坏帧计数
    }

    while not _exit_event.is_set():
        try:
            # 尝试抓取帧
            if not cap.grab():
                # 处理网络失败
                counters['consecutive_failures'] += 1
                if counters['consecutive_failures'] >= config['max_failures']:
                    err = f"control_capture_video: 连续抓取失败过多，需要重启整个流处理: {pull_stream_url}"
                    logger.error(err)
                    handle_error(err, err_queue)
                    break
                time.sleep(config['retry_interval'])
                continue

            # 解码帧
            ret, frame = cap.retrieve()
            if not (ret and frame is not None and frame.size > 0):
                # 处理网络失败
                counters['consecutive_failures'] += 1
                if counters['consecutive_failures'] >= config['max_failures']:
                    err = f"control_capture_video: 连续解码失败过多，需要重启整个流处理: {pull_stream_url}"
                    logger.error(err)
                    handle_error(err, err_queue)
                    break
                time.sleep(config['retry_interval'])
                continue

            # 验证帧完整性
            start_time = time.time()
            if validate_frame_integrity(frame):
                # logger.debug(f"帧验证耗时: {(time.time() - start_time) * 1000:.2f}ms")
                # 帧验证通过，重置计数器
                counters['consecutive_failures'] = 0
                counters['corrupted_frame_count'] = 0
                # 检查队列大小，如果超过阈值则丢弃帧
                if frame_queue.full():
                    try:
                        while not frame_queue.empty():
                            frame_queue.get_nowait()
                    except mp.queues.Empty:
                        pass
                frame_queue.put(frame)
            else:
                # 处理损坏帧
                counters['corrupted_frame_count'] += 1
                # 只在特定间隔输出警告，避免日志过多
                if counters['corrupted_frame_count'] % 5 == 1:  # 每5个损坏帧输出一次
                    logger.warning(f"control_capture_video: 检测到损坏帧，当前计数: {counters['corrupted_frame_count']}")

                if counters['corrupted_frame_count'] >= config['max_corrupted_frames']:
                    err = f"control_capture_video: 损坏帧过多({counters['corrupted_frame_count']})，需要重启整个流处理: {pull_stream_url}"
                    logger.error(err)
                    handle_error(err, err_queue)
                    break
                time.sleep(config['retry_interval'] / 2)

        except Exception as e:
            logger.error(f"control_capture_video异常: {str(e)}")
            # 处理异常导致的网络失败
            counters['consecutive_failures'] += 1
            if counters['consecutive_failures'] >= config['max_failures']:
                err = f"control_capture_video: 异常过多，需要重启整个流处理: {str(e)}"
                handle_error(err, err_queue)
                break
            time.sleep(config['retry_interval'])

    # 清理资源
    try:
        cap.release()
    except Exception as e:
        logger.warning(f"释放视频捕获对象时发生异常: {str(e)}")

    frame_queue.put(None)  # 发送结束信号
    logger.info("control_capture_video end")


def handle_gimbal_align_result(params):
    # 从算法结果中获取业务需要的参数
    status_msg = params.status_msg
    pitch_adjust = params.pitch_adjust
    yaw_adjust = params.yaw_adjust
    zoom_msg = params.zoom_msg
    gimbal_type_msg = params.gimbal_type_msg
    is_gimbal_adjust = params.is_gimbal_adjust
    isTakePhoto = params.is_take_photo
    is_zoom = params.is_zoom
    is_gimbal_type = params.is_gimbal_type

    action_type = None
    gimbal_control_resp = None

    if isTakePhoto:
        # 拍照
        action_type = 3

    elif is_gimbal_adjust or is_zoom or is_gimbal_type:
        # 云台调整
        action_type = 2

        zoom_type = 0
        if is_zoom:
            # 焦距调整
            if zoom_msg == "拉远焦距":
                zoom_type = 2
            elif zoom_msg == "拉近焦距":
                zoom_type = 1

        gimbal_type = 0
        if is_gimbal_type:
            # 云台模式切换
            if gimbal_type_msg == "变焦":
                gimbal_type = 1
            elif gimbal_type_msg == "广角":
                gimbal_type = 2

        gimbal_control_resp = {
            "pitch_adjust": pitch_adjust,
            "yaw_adjust": yaw_adjust,
            "zoom_type": zoom_type,
            "gimbal_type": gimbal_type,
        }

    return action_type, gimbal_control_resp


def draw_frame_with_gimbal_result(frame, detection_results, status_msg):
    height, width = frame.shape[:2]

    if detection_results and len(detection_results[0].boxes) > 0:
        for box in detection_results[0].boxes:
            cls_id = int(box.cls[0])
            cls_name = detection_results[0].names[cls_id]
            confidence = box.conf[0]
            threshold = CONFIDENCE_THRESHOLDS.get(cls_name, 0.35)

            if confidence > threshold:
                chinese_name, color = CLASS_NAME_MAPPING.get(cls_name, (cls_name, (0, 255, 0)))
                xyxy = box.xyxy[0].cpu().numpy().astype(int)

                cv2.rectangle(frame, (xyxy[0], xyxy[1]), (xyxy[2], xyxy[3]), color, 3)
                text = f"{chinese_name} {confidence:.2f}"
                frame = draw_chinese_text(frame, text, (xyxy[0], xyxy[1] - 20),
                                          font_size=20, color=color)

    if status_msg:
        frame = draw_chinese_text(frame, status_msg, (10, height - 50),
                                  font_size=20, color=(0, 255, 255))

    return frame


def device_control_process(job_id, drone_sn, dock_sn):
    """
    执行设备行为函数

    # 函数逻辑：
    # 注意；device_control也是一个单独的进程函数，在start_inspection_predict中启动
    # 1.轮询行为序列，根据行为来调用device_control.py的函数来执行设备动作，调用成功把当前行为元素移除，然后将接着执行下一个行为
    # 2.目前device_control.py暂未实现，这是v2功能，但是不影响这个函数的基本实现，留好位置下次迭代即可
    # 3.行为-云台对准拍照是特殊行为，不是单纯一次调用设备控制接口就能完成，具体逻辑为：
    #   a)行为序列有云台对准拍照, gimbal_control_enabled = true。然后轮询等待执行该行为的子行为序列动作
    #   b)process_frames函数的云台对准算子会自动调用算子得到对准决策，然后放到子行为序列动作中
    #   c)执行子行为动作
    #       如果是action_type = 3(拍照)，则代表本次云台对准拍照行为已成功，执行后下一个行为
    #       如果是其他，则执行调用设备控制后，更新状态gimbal_control_enabled = true

    """
    logger.info("设备控制进程启动")

    while not _exit_event.is_set():
        if not drone_fly_doing or not drone_fly_doing.value:
            continue

        try:
            # 1. 轮询主行为序列
            try:
                action_data = device_action_queue.get(timeout=0.1)
                logger.info(f"获取到设备行为：{action_data}")

                action_type = action_data.get('action_type')

                # 记录当前正在执行的行为类型
                update_current_executing_action_type(action_type)

                if action_type == 'gimbal_align_photo':
                    # 云台对准拍照特殊行为
                    logger.info("开始执行云台对准拍照行为")

                    # 开启云台控制
                    update_gimbal_control(True)

                    # 轮询等待子行为序列
                    gimbal_align_completed = False
                    while not _exit_event.is_set() and not gimbal_align_completed:
                        try:
                            sub_action = gimbal_sub_action_queue.get(timeout=0.1)
                            logger.info(f"获取到云台子行为：{sub_action}")

                            sub_action_type = sub_action.get('action_type')

                            if sub_action_type == 3:  # 拍照
                                # 执行拍照动作
                                success = execute_device_action(sub_action, job_id, drone_sn, dock_sn)
                                if success:
                                    logger.info("云台对准拍照行为完成")
                                    gimbal_align_completed = True
                                    # 执行状态更新回调
                                    if 'state_updates' in action_data:
                                        execute_state_updates(action_data['state_updates'])
                                else:
                                    logger.error("拍照执行失败")
                                    update_gimbal_control(True)
                            else:
                                # 其他云台控制动作
                                success = execute_device_action(sub_action, job_id, drone_sn, dock_sn)
                                if not success:
                                    logger.error(f"云台控制动作执行失败：{sub_action}")

                                # 继续保持云台控制开启状态
                                update_gimbal_control(True)

                        except mp.queues.Empty:
                            continue
                        except Exception as e:
                            logger.error(f"处理云台子行为时发生错误：{str(e)}")
                            continue

                    # 云台对准拍照行为执行完成，重置当前执行行为类型
                    update_current_executing_action_type(None)

                else:
                    # 普通设备行为
                    success = execute_device_action(action_data, job_id, drone_sn, dock_sn)
                    if success:
                        logger.info(f"设备行为执行成功：{action_data}")
                        # 执行状态更新回调
                        if 'state_updates' in action_data:
                            execute_state_updates(action_data['state_updates'])
                    else:
                        logger.error(f"设备行为执行失败：{action_data}")

                    # 普通设备行为执行完成，重置当前执行行为类型
                    update_current_executing_action_type(None)

            except mp.queues.Empty:
                continue
            except Exception as e:
                logger.error(f"处理设备行为时发生错误：{str(e)}")
                continue

        except Exception as e:
            logger.error(f"设备控制进程发生错误：{str(e)}")
            time.sleep(0.1)

    logger.info("设备控制进程结束")


def execute_device_action(action_data, job_id, drone_sn, dock_sn):
    """
    执行具体的设备动作
    根据不同的action_type调用device_control.py中对应的函数
    """
    action_type = action_data.get('action_type')

    try:
        if action_type == 1:  # 无人机控制
            drone_action = action_data.get('drone_action', {})
            logger.info(f"执行无人机控制: {drone_action}")
            success = device_control.control_drone(drone_sn, dock_sn, drone_action)
            if success:
                logger.info(f"无人机控制成功: {drone_action}")
            else:
                logger.error(f"无人机控制失败: {drone_action}")
            return success

        elif action_type == 2:  # 云台控制
            gimbal_data = action_data.get('gimbal_control_data', {})
            logger.info(f"执行云台控制: {gimbal_data}")
            success = device_control.control_gimbal(drone_sn, dock_sn, gimbal_data)
            if success:
                logger.info(f"云台控制成功: {gimbal_data}")
            else:
                logger.error(f"云台控制失败: {gimbal_data}")
            return success

        elif action_type == 3:  # 拍照
            logger.info("执行拍照")
            success = device_control.take_photo(drone_sn, dock_sn)
            if success:
                logger.info("拍照成功")
            else:
                logger.error("拍照失败")
            return success

        else:
            logger.warning(f"未知的设备动作类型：{action_type}")
            return False

    except Exception as e:
        logger.error(f"执行设备动作时发生异常: action_type={action_type}, error={str(e)}")
        return False


def execute_state_updates(state_updates):
    """执行状态更新回调"""
    for update in state_updates:
        update_type = update.get('type')

        if update_type == 'drone_fly_context':
            key = update.get('key')
            value = update.get('value')
            update_drone_fly_context(key, value)

        elif update_type == 'drone_fly_context_clear':
            # 支持清空drone_fly_context中的列表字段
            key = update.get('key')
            if key and drone_fly_context and key in drone_fly_context:
                try:
                    # 对于multiprocessing.Manager().dict()，需要先获取列表，清空后重新赋值
                    field_value = drone_fly_context[key]
                    if hasattr(field_value, 'clear'):
                        field_value.clear()
                        # 重新赋值以确保多进程同步
                        drone_fly_context[key] = field_value
                        logger.info(f"已清空绕飞巡检上下文字段：{key}")
                    else:
                        logger.warning(f"绕飞巡检上下文字段 {key} 不支持clear操作")
                except Exception as e:
                    logger.error(f"清空绕飞巡检上下文字段 {key} 时发生错误：{str(e)}")
            else:
                logger.warning(f"绕飞巡检上下文字段 {key} 不存在")

        elif update_type == 'drone_fly_doing':
            value = update.get('value')
            update_drone_fly_doing(value)

        elif update_type == 'gimbal_control':
            value = update.get('value')
            update_gimbal_control(value)

        elif update_type == 'depth_control':
            value = update.get('value')
            update_depth_control(value)

        else:
            logger.warning(f"未知的状态更新类型：{update_type}")


def process_frames(frame_queue, processed_frame_queue, message_queue, target_classes, job_id, drone_sn, dock_sn,
                   service_type):
    """控制模式的帧处理进程"""
    frame_count = 0
    start_time = time.time()
    last_log_time = start_time
    frames_processed = 0
    fps = 0.0

    while not _exit_event.is_set():
        try:
            # 检查处理队列大小，如果超过阈值则丢弃一些帧
            if processed_frame_queue.full():
                try:
                    while not processed_frame_queue.empty():
                        processed_frame_queue.get_nowait()
                except mp.queues.Empty:
                    pass

            # 获取单帧进行处理（不使用批处理）
            try:
                frame = frame_queue.get(timeout=0.01)
                if frame is None:
                    break
            except mp.queues.Empty:
                continue

            frame_count += 1
            frames_processed += 1
            current_time = time.time()

            # 每秒更新一次FPS显示
            if current_time - last_log_time >= 1.0:
                fps = frames_processed / (current_time - last_log_time)
                frames_processed = 0
                last_log_time = current_time

            height, width = frame.shape[:2]

            # 附带绘制结果的帧
            drew_frame = frame.copy()

            # 1.调用各巡检算子，得到设备行为决策以及绘制帧
            # 绕飞巡检算子
            if (drone_fly_context and drone_fly_context["depth_control"]) and (
                    drone_fly_doing and not drone_fly_doing.value):
                if drone_fly_context and not drone_fly_context["last_depth_control"]:
                    logger.info(f"绕飞巡检算子: frame = {frame_count}, 开始第一面采集")

                    # a.第一面采集
                    update_drone_fly_context("fly_count", 0)
                    update_drone_fly_context("gimbal_align_count", 0)
                    update_drone_fly_context("detect_state", 0)
                    drone_fly_context["depth_buffer"].clear()

                    clear_device_action()

                    # 1.行为-云台对准拍照
                    add_device_action({
                        'action_type': 'gimbal_align_photo',
                        'state_updates': [
                            # 更新状态-context["gimbal_align_count"] += 1
                            {'type': 'drone_fly_context', 'key': 'gimbal_align_count',
                             'value': drone_fly_context["gimbal_align_count"] + 1},
                            # 更新状态-update_drone_fly_doing(False)
                            {'type': 'drone_fly_doing', 'value': False}
                        ]
                    })

                    # 开启巡检执行锁
                    update_drone_fly_doing(True)

                elif drone_fly_context and frame_count % 5 == 0:
                    # b.其他面采集
                    try:
                        depth = estimate_depth(frame)
                        drone_fly_context["depth_buffer"].append(depth)
                        if len(drone_fly_context["depth_buffer"]) > 20:
                            drone_fly_context["depth_buffer"].pop(0)

                        logger.info(f"绕飞巡检算子: frame = {frame_count}, 开启其他面采集, 中心深度为 {depth:.3f} 米")
                    except Exception as e:
                        logger.error(f"深度估计算法调用失败: {str(e)}")
                        continue

                    log_fly_count = drone_fly_context["fly_count"]
                    log_depth_buffer = len(drone_fly_context["depth_buffer"])

                    logger.info(f"fly_count: {log_fly_count}, len depth_buffer: {log_depth_buffer}")

                    # 只有当绕飞未完成(fly_count < 4)且深度缓冲区有足够数据(>= 5)时才进行深度变化检测
                    if drone_fly_context["fly_count"] < 4 and len(drone_fly_context["depth_buffer"]) >= 5:
                    # if drone_fly_context["fly_count"] < 4:
                        current = drone_fly_context["depth_buffer"][-1]
                        recent_avg = np.mean(drone_fly_context["depth_buffer"][-5:])

                        clear_device_action()


                        if abs(current - recent_avg) > drone_fly_context["depth_threshold"]:
                            logger.info(f"current: {current}, recent_avg: {recent_avg}")
                            if drone_fly_context["detect_state"] == 0:
                                logger.info(
                                    f"绕飞巡检算子: [绕飞面 {drone_fly_context['fly_count'] + 1}] 第一次深度剧变")

                                if drone_fly_context['fly_count'] == 0:
                                    # 第一面
                                    logger.info("无人机右移 p 米，p为无人机两三个身位")

                                    add_device_action({
                                        'action_type': 1,
                                        'drone_action': {
                                            'x': 0,
                                            'y': drone_control_p,
                                            'h': 0,
                                            'w': 0
                                        },
                                        'state_updates': []
                                    })

                                    logger.info("左转90度")

                                    add_device_action({
                                        'action_type': 1,
                                        'drone_action': {
                                            'x': 0,
                                            'y': 0,
                                            'h': 0,
                                            'w': -90
                                        },
                                        'state_updates': [
                                            {'type': 'drone_fly_context_clear', 'key': 'depth_buffer'},
                                            # 更新状态-context["depth_buffer"].clear()
                                            {'type': 'drone_fly_context', 'key': 'detect_state', 'value': 1},
                                            # 更新状态-context["detect_state"] = 1
                                            {'type': 'drone_fly_doing', 'value': False}
                                            # 更新状态-update_drone_fly_doing(False)
                                        ]
                                    })

                                elif context['fly_count'] == 1:
                                    # 第二面穿桥出来了
                                    logger.info("左转90度")

                                    add_device_action({
                                        'action_type': 1,
                                        'drone_action': {
                                            'x': 0,
                                            'y': 0,
                                            'h': 0,
                                            'w': -90
                                        },
                                        'state_updates': []
                                    })

                                    logger.info("无人机右移 p 米，p为无人机两三个身位")

                                    add_device_action({
                                        'action_type': 1,
                                        'drone_action': {
                                            'x': 0,
                                            'y': drone_control_p,
                                            'h': 0,
                                            'w': 0
                                        },
                                        'state_updates': [
                                            {'type': 'drone_fly_context_clear', 'key': 'depth_buffer'},
                                            # 更新状态-context["depth_buffer"].clear()
                                            {'type': 'drone_fly_context', 'key': 'detect_state', 'value': 1},
                                            # 更新状态-context["detect_state"] = 1
                                            {'type': 'drone_fly_doing', 'value': False}
                                            # 更新状态-update_drone_fly_doing(False)
                                        ]})

                                elif context['fly_count'] == 2:
                                    # 第三面右侧了
                                    logger.info("左转90度")

                                    add_device_action({
                                        'action_type': 1,
                                        'drone_action': {
                                            'x': 0,
                                            'y': 0,
                                            'h': 0,
                                            'w': -90
                                        },
                                        'state_updates': []
                                    })

                                    logger.info("无人机右移 n 米，推桥底中间")

                                    add_device_action({
                                        'action_type': 1,
                                        'drone_action': {
                                            'x': 0,
                                            'y': drone_control_n,
                                            'h': 0,
                                            'w': 0
                                        },
                                        'state_updates': [
                                            {'type': 'drone_fly_context_clear', 'key': 'depth_buffer'},
                                            # 更新状态-context["depth_buffer"].clear()
                                            {'type': 'drone_fly_context', 'key': 'detect_state', 'value': 1},
                                            # 更新状态-context["detect_state"] = 1
                                            {'type': 'drone_fly_doing', 'value': False}
                                            # 更新状态-update_drone_fly_doing(False)
                                        ]
                                    })

                                elif context['fly_count'] == 3:
                                    # 第四面穿桥出来了
                                    logger.info("左转90度")

                                    add_device_action({
                                        'action_type': 1,
                                        'drone_action': {
                                            'x': 0,
                                            'y': 0,
                                            'h': 0,
                                            'w': -90
                                        },
                                        'state_updates': []
                                    })

                                    logger.info("无人机右移 p 米，p为无人机两三个身位")

                                    add_device_action({
                                        'action_type': 1,
                                        'drone_action': {
                                            'x': 0,
                                            'y': drone_control_p,
                                            'h': 0,
                                            'w': 0
                                        },
                                        'state_updates': [
                                            {'type': 'drone_fly_context_clear', 'key': 'depth_buffer'},
                                            # 更新状态-context["depth_buffer"].clear()
                                            {'type': 'drone_fly_context', 'key': 'detect_state', 'value': 1},
                                            # 更新状态-context["detect_state"] = 1
                                            {'type': 'drone_fly_doing', 'value': False}
                                            # 更新状态-update_drone_fly_doing(False)
                                        ]})

                                # 更新设备行为序列
                                # 1.行为-无人机朝机头方向推进 p 米，p为无人机两三个身位
                                # add_device_action({
                                #     'action_type': 1,
                                #     'drone_action': {
                                #         'x': 2.0,
                                #         'y': 0,
                                #         'h': 0,
                                #         'w': 0
                                #     },
                                #     'state_updates': []
                                # })

                                # 2.行为-机头右转90°
                                # add_device_action({
                                #     'action_type': 1,
                                #     'drone_action': {
                                #         'x': 0,
                                #         'y': 0,
                                #         'h': 0,
                                #         'w': 90
                                #     },
                                #     'state_updates': []
                                # })

                                # 3.行为-云台右转90°
                                # add_device_action({
                                #     'action_type': 2,
                                #     'gimbal_control_data': {
                                #         "pitch_adjust": 0,
                                #         "yaw_adjust": 90,
                                #         "zoom_type": 0,
                                #         "gimbal_type": 0,
                                #     },
                                #     'state_updates': [
                                #         {'type': 'drone_fly_context_clear', 'key': 'depth_buffer'},
                                #         # 更新状态-context["depth_buffer"].clear()
                                #         {'type': 'drone_fly_context', 'key': 'detect_state', 'value': 1},
                                #         # 更新状态-context["detect_state"] = 1
                                #         {'type': 'drone_fly_doing', 'value': False}
                                #         # 更新状态-update_drone_fly_doing(False)
                                #     ]
                                # })

                            elif drone_fly_context["detect_state"] == 1:
                                logger.info(
                                    f"绕飞巡检算子: [绕飞面 {drone_fly_context['fly_count'] + 1}] 第二次深度剧变")

                                if drone_fly_context["gimbal_align_count"] < 4:
                                    logger.info(f"绕飞巡检算子: 正在执行 {drone_fly_context['fly_count']}/4 面绕飞")

                                    if context['fly_count'] == 0:
                                        # 右移进第二面
                                        logger.info("无人机右移 n 米，推桥底中间")

                                        add_device_action({
                                            'action_type': 1,
                                            'drone_action': {
                                                'x': 0,
                                                'y': drone_control_n,
                                                'h': 0,
                                                'w': 0
                                            },
                                            'state_updates': []
                                        })

                                    elif context['fly_count'] == 1:
                                        # 到第三面了
                                        pass
                                    elif context['fly_count'] == 2:
                                        # 到第四面了
                                        pass
                                    elif context['fly_count'] == 3:
                                        # 回到初始位置
                                        pass

                                    # 1.行为-无人机沿着机头方向推进 n 米，推到桥底中间
                                    # add_device_action({
                                    #     'action_type': 1,
                                    #     'drone_action': {
                                    #         'x': 8.0,
                                    #         'y': 0,
                                    #         'h': 0,
                                    #         'w': 0
                                    #     },
                                    #     'state_updates': []
                                    # })

                                    # 2.行为-云台对准拍照

                                    add_device_action({
                                        'action_type': 'gimbal_align_photo',
                                        'state_updates': [
                                            {'type': 'drone_fly_context', 'key': 'gimbal_align_count',
                                             # 更新状态-context["gimbal_align_count"] += 1
                                             'value': drone_fly_context["gimbal_align_count"] + 1},
                                            {'type': 'drone_fly_context', 'key': 'fly_count',
                                             # 更新状态-context["fly_count"] += 1
                                             'value': drone_fly_context["fly_count"] + 1},
                                            {'type': 'drone_fly_context', 'key': 'detect_state', 'value': 0},
                                            # 更新状态-context["detect_state"] = 0
                                            {'type': 'drone_fly_doing', 'value': False}
                                            # 更新状态-update_drone_fly_doing(False)
                                        ]
                                    })
                                else:
                                    logger.info(f"绕飞巡检算子: 云台对准算法控制次数已达上限")

                                    if context['fly_count'] == 0:
                                        # 右移进第二面
                                        logger.info("无人机右移 n 米，推桥底中间")

                                        add_device_action({
                                            'action_type': 1,
                                            'drone_action': {
                                                'x': 0,
                                                'y': drone_control_n,
                                                'h': 0,
                                                'w': 0
                                            },
                                            'state_updates': [
                                                {'type': 'drone_fly_context', 'key': 'fly_count',
                                                 # 更新状态-context["fly_count"] += 1
                                                 'value': drone_fly_context["fly_count"] + 1},
                                                {'type': 'drone_fly_context', 'key': 'detect_state', 'value': 0},
                                                # 更新状态-context["detect_state"] = 0
                                                {'type': 'drone_fly_doing', 'value': False}
                                                # 更新状态-update_drone_fly_doing(False)
                                            ]
                                        })
                                    elif context['fly_count'] == 1:
                                        # 到第三面了
                                        pass
                                    elif context['fly_count'] == 2:
                                        # 到第四面了
                                        pass

                                    # 1.行为-无人机沿着机头方向推进 n 米，推到桥底中间
                                    # add_device_action({
                                    #     'action_type': 1,
                                    #     'drone_action': {
                                    #         'x': 8,
                                    #         'y': 0,
                                    #         'h': 0,
                                    #         'w': 0
                                    #     },
                                    #     'state_updates': [
                                    #         {'type': 'drone_fly_context', 'key': 'fly_count',
                                    #          # 更新状态-context["fly_count"] += 1
                                    #          'value': drone_fly_context["fly_count"] + 1},
                                    #         {'type': 'drone_fly_context', 'key': 'detect_state', 'value': 0},
                                    #         # 更新状态-context["detect_state"] = 0
                                    #         {'type': 'drone_fly_doing', 'value': False}
                                    #         # 更新状态-update_drone_fly_doing(False)
                                    #     ]
                                    # })
                        else:
                            # 1.行为-无人机右移 1 米
                            logger.info(f"绕飞巡检算子: frame = {frame_count}, 无人机右移 1 米")

                            add_device_action({
                                'action_type': 1,
                                'drone_action': {
                                    'x': 0,
                                    'y': 1,
                                    'h': 0,
                                    'w': 0
                                },
                                'state_updates': [
                                    {'type': 'drone_fly_doing', 'value': False}  # 更新状态-update_drone_fly_doing(False)
                                ]
                            })

                            # add_device_action({
                            #     'action_type': 1,
                            #     'drone_action': {
                            #         'x': 1,
                            #         'y': 0,
                            #         'h': 0,
                            #         'w': 0
                            #     },
                            #     'state_updates': [
                            #         {'type': 'drone_fly_doing', 'value': False}  # 更新状态-update_drone_fly_doing(False)
                            #     ]
                            # })

                        # 开启巡检执行锁

                        update_drone_fly_doing(True)

                update_drone_fly_context("last_depth_control", drone_fly_context["depth_control"])

                if drone_fly_context["fly_count"] >= 4:
                    logger.info("绕飞巡检算子: 当前桥墩绕飞完成，退出绕飞控制")
                    update_drone_fly_context("depth_control", False)

                    # 告诉业务修改状态
                    control_resp_data = {
                        "job_id": job_id,
                        "drone_sn": drone_sn,
                        "dock_sn": dock_sn,
                        "type": 1,  # 1-完成单次绕飞巡检
                    }

                    message_data = {
                        'service_type': service_type,
                        'resp_data': control_resp_data,
                    }

                    logger.info(f"message_queue put:{control_resp_data}")
                    message_queue.put(message_data)

            # 云台对准算子
            if gimbal_control_enabled and gimbal_control_enabled.value:
                logger.info("开始调用云台对准算法")

                try:
                    # 调用云台对准算法
                    align_result = process_gimbal_alignment(
                        frame=frame,
                        frame_width=width,
                        frame_height=height,
                        frame_count=frame_count,
                        target_classes=target_classes,
                    )
                except Exception as e:
                    logger.error(f"云台对准算法调用失败: {str(e)}")
                    continue

                # 解析云台对准算法结果，获取云台行为决策
                device_action_type, gimbal_control_data = handle_gimbal_align_result(align_result)

                # 绘制检测框
                drew_frame = draw_frame_with_gimbal_result(drew_frame, align_result.detection_results,
                                                           align_result.status_msg)

                if align_result.status_msg:
                    logger.info(f"云台对准算子: status_msg= {align_result.status_msg}")

                if gimbal_control_data:
                    logger.info(f"云台对准算子: 控制数据= {gimbal_control_data}")

                update_gimbal_control(False)

                action_data = {
                    'action_type': device_action_type,
                    'gimbal_control_data': gimbal_control_data
                }

                # 判断当前正在执行的行为是否是云台对准拍照
                current_action_type = get_current_executing_action_type()

                logger.info(
                    f"drone_fly_doing: {drone_fly_doing}, current_executing_action_type: {current_action_type}")

                if drone_fly_doing and drone_fly_doing.value and current_action_type == 'gimbal_align_photo':
                    logger.info("检测到当前行为是云台对准拍照，将控制数据添加到子行为序列")
                    # 将云台控制数据添加到子行为序列
                    add_gimbal_sub_action(action_data)
                else:
                    logger.info("当前不是云台对准拍照行为，跳过子行为序列添加")

                    # if device_action_type == 3:  # 拍照
                    #     # 执行拍照动作
                    #     success = execute_device_action(action_data, job_id, drone_sn, dock_sn)
                    #     if success:
                    #         logger.info("云台对准拍照行为完成")
                    #         # todo
                    #     else:
                    #         logger.error("拍照执行失败")
                    #
                    #         update_gimbal_control(True)
                    #
                    # else:
                    #     # 其他云台控制动作
                    #     success = execute_device_action(action_data, job_id, drone_sn, dock_sn)
                    #     if not success:
                    #         logger.error(f"云台控制动作执行失败：{action_data}")
                    #
                    #     update_gimbal_control(True)

            # 绘制FPS和时间信息
            current_time_str = time.strftime("%H:%M:%S", time.localtime())
            # logger.info(f"绘制帧 {frame_count}")
            drew_frame = draw_fps_time(drew_frame, fps, current_time_str, frame_count)

            # 将处理后的帧放入队列
            processed_frame_queue.put(drew_frame)

        except Exception as e:
            logger.error(f"控制处理帧时发生错误: {str(e)}")
            continue

    processed_frame_queue.put(None)  # Signal that processing has ended
    logger.info("control_process_frames end")


def push_to_ffmpeg(pull_stream_url, rtmp_output, processed_frame_queue, err_queue):
    """控制模式的FFmpeg推流进程"""
    cap_temp = cv2.VideoCapture(pull_stream_url)
    width = int(cap_temp.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap_temp.get(cv2.CAP_PROP_FRAME_HEIGHT))
    cap_temp.release()

    # command_push = [
    #     'ffmpeg',
    #     '-y',  # 覆盖输出文件
    #     '-re',  # 以本地帧率读取输入
    #     '-fflags', 'nobuffer',  # 禁用输入缓冲
    #     "-hwaccel", "cuda",  # 硬件加速
    #     "-hwaccel_output_format", "yuv420p",
    #
    #     # 输入相关参数
    #     '-f', 'rawvideo',
    #     '-vcodec', 'rawvideo',
    #     '-pix_fmt', 'bgr24',
    #     '-s', f'{width}x{height}',
    #     '-i', '-',
    #
    #     # 编码相关参数
    #     '-c:v', 'h264_nvenc',  # 使用cuda硬件编码
    #     '-profile:v', 'main',
    #     "-pix_fmt", "yuv420p",  # 输出像素格式 (兼容性最佳)
    #
    #     # 输出格式
    #     '-f', 'flv',
    #     rtmp_output
    # ]

    command_push = [
        'ffmpeg',
        '-y',  # 覆盖输出文件
        '-re',  # 以本地帧率读取输入
        '-fflags', 'nobuffer',  # 禁用输入缓冲

        # 输入相关参数
        '-f', 'rawvideo',
        '-vcodec', 'rawvideo',
        '-pix_fmt', 'bgr24',
        '-s', f'{width}x{height}',
        '-i', '-',

        # 编码相关参数
        '-c:v', 'libx264',
        '-profile:v', 'main',
        "-pix_fmt", "yuv420p",  # 输出像素格式 (兼容性最佳)

        # 输出格式
        '-f', 'flv',
        rtmp_output
    ]

    process = subprocess.Popen(command_push, stdin=subprocess.PIPE, stdout=subprocess.DEVNULL,
                               stderr=subprocess.DEVNULL, bufsize=0)

    while not _exit_event.is_set():
        try:
            frame = processed_frame_queue.get(timeout=0.01)
        except mp.queues.Empty:
            continue

        if frame is None:
            break

        try:
            process.stdin.write(frame.tobytes())
            process.stdin.flush()
        except Exception as e:
            err = f"control_push_to_ffmpeg err: {e}"
            logger.error(err)
            handle_error(err, err_queue)
            break

    logger.info("control_push_to_ffmpeg end")
    process.stdin.close()
    process.wait()


def start_inspection_predict(ws_client, data, service_type):
    """
    直播流检测主函数，使用三进程架构进行控制决策 - 支持自动重启。
    """
    # 无人机云台直播拉流地址
    pull_stream_url = data.get("pull_stream_url")
    # 处理后的推流地址
    push_stream_url = data.get("push_stream_url")
    # 任务id
    job_id = data.get("job_id")
    # 检测目标
    # target_classes = data.get("target_classes")
    target_classes = ["demo", "kk"]
    # 业务透传
    drone_sn = data.get("drone_sn")
    dock_sn = data.get("dock_sn")

    # 创建多进程共享变量（在重启循环外创建，避免重复创建）
    global gimbal_control_enabled, drone_fly_doing, drone_fly_context
    global device_action_queue, gimbal_sub_action_queue, current_executing_action_type

    # 使用 Manager 创建多进程安全的变量和字典
    manager = mp.Manager()

    gimbal_control_enabled = mp.Value('b', False)  # bool类型
    drone_fly_doing = mp.Value('b', False)  # bool类型
    current_executing_action_type = manager.Value('c', "")  # 字符串类型，用来存储当前执行的行为类型
    drone_fly_context = manager.dict(init_drone_fly_context())

    # 创建行为序列队列
    device_action_queue = mp.Queue()  # 设备行为序列队列
    gimbal_sub_action_queue = mp.Queue()  # 云台子行为序列队列

    shared_vars = {
        'gimbal_control_enabled': gimbal_control_enabled,
        'drone_fly_doing': drone_fly_doing,
        'drone_fly_context': drone_fly_context,
        'device_action_queue': device_action_queue,
        'gimbal_sub_action_queue': gimbal_sub_action_queue,
        'current_executing_action_type': current_executing_action_type,
    }

    max_restart_attempts = 3  # 最大重启次数
    restart_count = 0
    restart_delay = 2  # 重启延迟（秒）

    while restart_count <= max_restart_attempts:
        try:
            # 创建新的退出事件和队列
            create_exit_event()

            # 重置云台对准算法状态
            reset_gimbal_align_state()

            frame_queue = mp.Queue(maxsize=5)
            processed_frame_queue = mp.Queue(maxsize=5)
            err_queue = mp.Queue()
            message_queue = mp.Queue()  # 新增消息队列，用于子进程向主进程发送消息

            processes = [
                mp.Process(name="control_capture_process",
                           target=run_capture_video,
                           args=(_exit_event, shared_vars, pull_stream_url, frame_queue, err_queue)),
                mp.Process(name="control_process_frames_process",
                           target=run_process_frames,
                           args=(_exit_event, shared_vars, frame_queue, processed_frame_queue,
                                 message_queue,
                                 target_classes, job_id, drone_sn, dock_sn, service_type)),
                mp.Process(name="control_push_process",
                           target=run_push_to_ffmpeg,
                           args=(
                               _exit_event, shared_vars, pull_stream_url, push_stream_url,
                               processed_frame_queue,
                               err_queue)),
                mp.Process(name="device_control_process",
                           target=run_device_control,
                           args=(_exit_event, shared_vars, job_id, drone_sn, dock_sn))
            ]

            # 启动所有进程
            for p in processes:
                p.start()

            logger.info(f"无人机控制流处理进程启动完成 (尝试次数: {restart_count + 1})")

            # 监控进程状态
            while any(p.is_alive() for p in processes):
                try:
                    # 检查错误队列
                    err = err_queue.get_nowait()
                    logger.error(f"drone_control_predict err_queue: {err}")

                    # 清理当前进程
                    _exit_event.set()
                    for p in processes:
                        p.join(timeout=5)
                        if p.is_alive():
                            p.terminate()

                    # 判断是否需要重启
                    if restart_count < max_restart_attempts:
                        restart_count += 1
                        logger.warning(f"无人机控制流处理出错，{restart_delay}秒后重启 (第{restart_count}次重启)")
                        time.sleep(restart_delay)
                        break  # 跳出监控循环，进入重启流程
                    else:
                        logger.error(f"达到最大重启次数({max_restart_attempts})，停止无人机控制流处理")
                        return f"无人机控制流处理失败，已重启{restart_count}次: {err}"

                except mp.queues.Empty:
                    # 检查消息队列
                    try:
                        message_data = message_queue.get_nowait()

                        service_type_msg = message_data['service_type']
                        resp_data = message_data['resp_data']

                        # 使用主进程中的ws_client发送消息
                        ws_client.send_message(make_msg_resp(service_type_msg, data=resp_data))
                        logger.info(f"drone_control_predict send_message: {resp_data}")

                    except mp.queues.Empty:
                        pass

                    time.sleep(0.1)  # 短暂休眠避免CPU过度使用
            else:
                # 所有进程正常结束
                logger.info("无人机控制流处理正常结束")
                return None

        except Exception as e:
            logger.error(f"drone_control_predict异常: {str(e)}")
            if restart_count < max_restart_attempts:
                restart_count += 1
                logger.warning(f"无人机控制流处理异常，{restart_delay}秒后重启 (第{restart_count}次重启)")
                time.sleep(restart_delay)
            else:
                logger.error(f"达到最大重启次数({max_restart_attempts})，停止无人机控制流处理")
                return f"无人机控制流处理异常失败: {str(e)}"

    # 达到最大重启次数后的返回
    logger.error(f"无人机控制流处理达到最大重启次数({max_restart_attempts})，停止处理")
    return f"无人机控制流处理失败，已重启{restart_count}次"
